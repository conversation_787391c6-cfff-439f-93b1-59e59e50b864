import { useEffect, useState } from 'react';
import {
  <PERSON>alog,
  Popover,
  Button as RA<PERSON><PERSON><PERSON><PERSON>,
  Tag,
  TagGroup,
  TagList, DatePicker,
} from 'react-aria-components';

import { Calendar as CalenderIcon } from 'lucide-react';
import { Label, TextArea } from '@/components/ui/Field.tsx';
import { But<PERSON> } from '@/components/ui/button.tsx';
import { Calendar } from '@/components/ui/calendar.tsx';

import clsx from 'clsx';
import {DateTimeSelector} from "@/components/ui/date-time-selector.tsx";
import { fromDate, getLocalTimeZone, toCalendarDate } from '@internationalized/date';

export interface Procedure {
  id: string;
  unit_id: number;
  name: string;
  seq_number: number;
}

interface StudyRequestItem {
  id: number;
  procedure_ids?: number[];
  provisional_procedure_date?: string;
  note?: string;
}

interface ProcedureTagListProps {
  availableProcedures: Procedure[];
  selectedProcedureIds?: number[];
  onSelectionChange: (procedureIds: number[]) => void;
}

function ProcedureTagList({ availableProcedures, selectedProcedureIds, onSelectionChange }: ProcedureTagListProps) {
  return (
    <div className="my-1">
      <TagGroup
        selectionMode="multiple"
        selectedKeys={selectedProcedureIds ? new Set(selectedProcedureIds.map(String)) : undefined}
        onSelectionChange={(keys) => {
          const procedureIds = Array.from(keys).map((key) => Number(key));
          onSelectionChange(procedureIds);
        }}
      >
        <Label className="mb-2 block text-xs text-neutral-700">Tests</Label>
        <TagList items={availableProcedures} className="flex flex-wrap gap-2">
          {(procedure) => (
              <Tag
                key={procedure.id}
                id={procedure.id}
                className={clsx(
                  'group text-brand-700 focus:bg-neutral-100 relative inline-flex hover:bg-neutral-100',
                  'cursor-pointer items-center rounded-2xl border border-neutral-400 px-3 py-1 text-xs transition-all delay-75 duration-150 hover:bg-neutral-100',
                  'data-[selected="true"]:border-brand-500 data-[selected="true"]:border-1.5 data-[selected="true"]:bg-brand-50'
                )}
              >
                {procedure.seq_number}. {procedure.name}
              </Tag>
          )}
        </TagList>
      </TagGroup>
    </div>
  );
}

function EditProcedureForStudyItem({
  procedures,
  studyRequestItem,
  onSubmit,
  onCancel
}: {
  procedures: Procedure[];
  studyRequestItem?: StudyRequestItem;
  onSubmit?: (data: any) => void;
  onCancel?: () => void;
}) {
  const [selectedProcedureIds, setSelectedProcedureIds] = useState<number[]>([]);
  const [provisionalDate, setProvisionalDate] = useState<string>('');
  const [note, setNote] = useState<string>('');
  const [errors, setErrors] = useState<{procedures?: string; date?: string}>({});

  // Initialize with existing data
  useEffect(() => {
    if (studyRequestItem) {
      setSelectedProcedureIds(studyRequestItem.procedure_ids || []);
      setProvisionalDate(studyRequestItem.provisional_procedure_date || '');
      setNote(studyRequestItem.note || '');
      setErrors({}); // Clear any previous errors
    }
  }, [studyRequestItem]);

  const handleSubmit = () => {
    // Validation
    const newErrors: {procedures?: string; date?: string} = {};

    if (!selectedProcedureIds || selectedProcedureIds.length === 0) {
      newErrors.procedures = 'Please select at least one procedure';
    }

    if (!provisionalDate) {
      newErrors.date = 'Please select a provisional date';
    }

    setErrors(newErrors);

    // If there are errors, don't submit
    if (Object.keys(newErrors).length > 0) {
      return;
    }

    if (onSubmit) {
      onSubmit({
        procedure_ids: selectedProcedureIds,
        provisional_procedure_date: provisionalDate,
        note: note,
      });
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <ProcedureTagList
          availableProcedures={procedures}
          selectedProcedureIds={selectedProcedureIds}
          onSelectionChange={(ids) => {
            setSelectedProcedureIds(ids);
            if (errors.procedures) {
              setErrors(prev => ({...prev, procedures: undefined}));
            }
          }}
        />
        {errors.procedures && (
          <div className="text-red-500 text-xs mt-1">{errors.procedures}</div>
        )}
      </div>

      <div>
        <Label className="text-neutral-700">Provisional Date</Label>
        <DatePicker
          granularity={"day"}
          hourCycle={24}
          shouldForceLeadingZeros
          className="react-aria-DatePicker"
          value={provisionalDate ? toCalendarDate(fromDate(new Date(provisionalDate), getLocalTimeZone())) : null}
          onChange={(dateTime) => {
            if (dateTime) {
              const date = dateTime.toString();
              setProvisionalDate(date ? date : '');
              if (errors.date) {
                setErrors(prev => ({...prev, date: undefined}));
              }
            }
          }}
        >
          <DateTimeSelector className="react-aria-Group w-full rounded-sm bg-white text-neutral-800">
            <RACButton className="react-aria-Button h-8">
              <CalenderIcon
                className="h-4 w-4"
                color="currentColor"
              />
            </RACButton>
          </DateTimeSelector>
          <Popover className="react-aria-Popover w-max">
            <Dialog>
              <Calendar />
            </Dialog>
          </Popover>
        </DatePicker>
        {errors.date && (
          <div className="text-red-500 text-xs mt-1">{errors.date}</div>
        )}
      </div>

      <div>
        <Label className="text-neutral-700">Test/Clinical Notes</Label>
        <TextArea
          size="md"
          value={note}
          onChange={(e) => setNote(e.target.value)}
        />
      </div>

      {(onSubmit || onCancel) && (
        <div className="mt-6 flex items-center justify-end gap-x-2">
          {onCancel && (
            <Button
              variant="outlined"
              size="small"
              onPress={() => {
                setErrors({}); // Clear errors when canceling
                onCancel();
              }}
            >
              Cancel
            </Button>
          )}
          {onSubmit && (
            <Button
              variant="outlined"
              color="brand"
              size="small"
              onPress={handleSubmit}
            >
              Save Changes
            </Button>
          )}
        </div>
      )}
    </div>
  );
}

export default EditProcedureForStudyItem;