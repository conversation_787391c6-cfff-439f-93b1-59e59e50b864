import {
  ReactNode,
  cloneElement,
  createContext,
  isValidElement,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import {ButtonContext, DEFAULT_SLOT, Provider} from 'react-aria-components';

import {CalendarDateTime} from '@internationalized/date';
import {mergeProps} from '@react-aria/utils';

import AdaptiveField, {ComboBoxProps, DateProps, SelectProps, TextOrNumberProps} from './AdaptiveField';

interface EditableState {
  isEditing: boolean;
  previewValue: string | number | CalendarDateTime;
  setPreviewValue: (value: string | number | CalendarDateTime) => void;
  setCurrentValue: (value: string | number) => void;
  currentValue: string;
  validationError: string | undefined;
  setIsEditing: (editing: boolean) => void;
  setValidationError: (error: string | undefined) => void;
  handleEdit: () => void;
  handleSubmit: () => void;
  handleCancel: () => void;
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleKeyDown: (e: React.KeyboardEvent) => void;
}

interface EditableProps {
  children: ReactNode | ((state: EditableState) => ReactNode);
  defaultValue?: string;
  // defaultPreviewValue?: string | number;
  value?: string;
  onValueChange?: (value: string) => void;
  onEdit?: () => void;
  onSubmit?: (value: string) => Promise<void> | string;
  onCancel?: () => void;
  validateFn?: (value: string) => string | undefined;
  disabled?: boolean;
  autoFocus?: boolean;
  cancelOnEscape?: boolean;
  exitOnClickOutside?: boolean;
  exitOnBlur?: boolean;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  handleDoubleClick?: (e: React.MouseEvent) => void;
  handleKeyDown?: (e: React.KeyboardEvent) => void;
}

const EditableContext = createContext<EditableState | null>(null);

const useEditable = () => {
  const context = useContext(EditableContext);
  if (!context) {
    throw new Error('Children may not be used outside Editable');
  }
  return context;
};

function Editable({
                    children,
                    defaultValue,
                    onKeyDown,
                    validateFn,
                    onSubmit,
                    value,
                    onValueChange,
                    ...props
                  }: EditableProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [validationError, setValidationError] = useState<string | undefined>(undefined);
  const [disabled, setDisabled] = useState(false);
  const [currentValue, setCurrentValue] = useState(defaultValue ?? '');
  const [previewValue, setPreviewValue] = useState<string | number | CalendarDateTime>(defaultValue ?? '');
  const componentRef = useRef<HTMLDivElement>(null);

  // Sync defaultValue with internal state when it changes
  useEffect(() => {
    if (defaultValue !== undefined) {
      setCurrentValue(defaultValue);
      setPreviewValue(defaultValue);
    }
  }, [defaultValue]);

  useEffect(() => {
    const handleOutsideClick = (e: MouseEvent) => {
      const popoverElement = document.querySelector('.react-aria-Popover');
      if (popoverElement && popoverElement.contains(e.target as Node)) {
        return;
      }
      if (isEditing && componentRef?.current && !componentRef?.current.contains(e.target as Node)) {
        handleCancel();
      }
    };

    document.addEventListener('mousedown', handleOutsideClick);
    return () => document.removeEventListener('mousedown', handleOutsideClick);
  }, [isEditing]);

  const handleEdit = () => {
    if (disabled) return;
    setIsEditing(true);
  };

  const handleSubmit = async () => {
    if (validateFn) {
      const error = validateFn(currentValue);
      if (error) {
        setValidationError(error);
        return;
      }
    }
    try {
      await onSubmit?.(currentValue);
      setIsEditing(false);
    } catch (error) {
      console.error('Submission error:', error);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setCurrentValue(defaultValue ?? ''); // Reset to defaultValue on cancel
    setPreviewValue(defaultValue ?? '');
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (validationError) {
      setValidationError(undefined);
    }
    setCurrentValue(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSubmit();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  const state = {
    isEditing,
    validationError,
    setIsEditing,
    setValidationError,
    setDisabled,
    handleEdit,
    handleCancel,
    handleSubmit,
    handleChange,
    handleKeyDown,
    previewValue,
    setPreviewValue,
    currentValue,
    setCurrentValue,
  } as EditableState;

  return (
    <EditableContext value={state}>
      <Provider
        values={[
          [
            ButtonContext,
            {
              slots: {
                [DEFAULT_SLOT]: {},
                submit: {
                  onPress: handleSubmit,
                  isDisabled: disabled,
                },
                cancel: {
                  onPress: handleCancel,
                  isDisabled: disabled,
                },
              },
            },
          ],
        ]}
      >
        <div
          {...props}
          ref={componentRef}
          data-editing={isEditing}
          data-disabled={disabled}
          onKeyDown={onKeyDown ? mergeProps(onKeyDown, handleKeyDown) : handleKeyDown}
        >
          {typeof children === 'function' ? children(state) : children}
        </div>
      </Provider>
    </EditableContext>
  );
}

function EditableTrigger({onDoubleClick, asChild, ...props}: any) {
  const {isEditing, handleEdit} = useEditable();

  if (isEditing) return null;

  if (asChild && isValidElement(props.children)) {
    return cloneElement(props.children, props);
  }

  return (
    <div
      {...props}
      onDoubleClick={onDoubleClick ? mergeProps(onDoubleClick, handleEdit) : handleEdit}
    >
      {props.children}
    </div>
  );
}

function EditableContent(props: any) {
  const {isEditing} = useEditable();

  if (!isEditing) return null;

  return <div {...props}>{props.children}</div>;
}

type ComboBoxEditableProps = Omit<ComboBoxProps, 'setValue'>;
type SelectEditableProps = Omit<SelectProps, 'setValue' | 'value'>;
type DateEditableProps = Omit<DateProps, 'setValue' | 'value'>;
type EditableValueProps = TextOrNumberProps | SelectEditableProps | DateEditableProps | ComboBoxEditableProps;

function EditableField({label, validateFn, ...props}: EditableValueProps) {
  const {
    previewValue,
    currentValue,
    setPreviewValue,
    setCurrentValue,
    handleKeyDown,
    setValidationError,
    handleChange,
  } = useEditable();

  const handleSelectionChange = (val: string) => {
    if (validateFn) {
      const error = validateFn(val);
      if (error) {
        setValidationError(error);
      }
    }
    setCurrentValue(val);
  };

  if (props.type === 'combobox') {
    const {placeholder, className, ref, onFocus, onBlur, ...rest} = props;
    return (
      <AdaptiveField
        setValue={setPreviewValue as any}
        handleChange={handleSelectionChange as any}
        label={label}
        validateFn={validateFn}
        ref={ref as any}
        onFocus={onFocus as any}
        onBlur={onBlur as any}
        className={className as string}
        placeholder={placeholder}
        {...rest}
      />
    );
  }

  if (props.type === 'select') {
    return (
      <AdaptiveField
        value={currentValue as any}
        setValue={setPreviewValue as any}
        onChange={handleSelectionChange as any}
        label={label}
        validateFn={validateFn}
        {...props}
      />
    );
  }

  return (
    <AdaptiveField
      {...props}
      value={previewValue as any}
      setValue={setPreviewValue as any}
      onChange={props.type === 'date' ? handleSelectionChange : (handleChange as any)}
      label={label}
      validateFn={validateFn}
      onKeyDown={handleKeyDown}
    />
  );
}

export default Object.assign(Editable, {
  Trigger: EditableTrigger,
  Content: EditableContent,
  Field: EditableField,
});
