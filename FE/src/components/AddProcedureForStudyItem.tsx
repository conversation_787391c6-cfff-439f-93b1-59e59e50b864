import clsx from 'clsx';
import {useEffect, useState} from 'react';
import {
  DateInput,
  DateSegment,
  Dialog,
  Group,
  ListBox,
  ListBoxItem,
  Popover,
  Button as RACButton,
  Select,
  SelectValue,
  Tag,
  TagList,
} from 'react-aria-components';

import {useQuery} from '@apollo/client';
import {Calendar as CalenderIcon, ChevronDown, Plus, X} from 'lucide-react';

import {Label, TextArea} from '@/components/ui/Field.tsx';
import {Button} from '@/components/ui/button.tsx';
import {Calendar} from '@/components/ui/calendar.tsx';
import {TextFormField} from '@/components/ui/form';
import {FieldArray} from '@/components/ui/form/FieldArray/FieldArray.tsx';
import {FieldArrayItems} from '@/components/ui/form/FieldArray/FieldArrayItems.tsx';
import {FieldError} from '@/components/ui/form/FieldError.tsx';
import {DateFormField} from '@/components/ui/form/fields/DateFormField.tsx';
import {TagGroupField} from '@/components/ui/form/fields/TagGroupField.tsx';
import {getUnitsList} from '@/graphql/lists.ts';
import {useFormContext} from 'react-hook-form';

interface Procedure {
  id: string;
  unit_id: number;
  name: string;
  seq_number: number;
}

interface ProcedureTagListProps {
  availableProcedures: Procedure[];
  index: number;
}

function ProcedureTagList({availableProcedures, index}: ProcedureTagListProps) {
  return (
    <div className="my-1">
      <TagGroupField
        name={`procedures.${index}.procedure_ids`}
      >
        <Label className="mb-2 block text-xs text-neutral-700">Tests</Label>
        <TagList
          items={availableProcedures}
          className="flex flex-wrap gap-2"
        >
          {(procedure) => (
            <Tag
              key={procedure.id}
              id={procedure.id}
              className={clsx(
                'group text-brand-700 relative inline-flex hover:bg-neutral-100 focus:bg-neutral-100',
                'cursor-pointer items-center rounded-2xl border border-neutral-400 px-3 py-1 text-xs transition-all delay-75 duration-150 hover:bg-neutral-100',
                'data-[selected="true"]:border-brand-500 data-[selected="true"]:border-1.5 data-[selected="true"]:bg-brand-50'
              )}
            >
              {procedure.seq_number}. {procedure.name}
            </Tag>
          )}
        </TagList>
      </TagGroupField>
    </div>
  );
}

function AddProcedureForStudyItem({procedures}: {procedures: Procedure[]}) {
  const {data: unitsData} = useQuery(getUnitsList);
  const [selectedUnit, setSelectedUnit] = useState<number | undefined>(undefined);
  const [availableProcedures, setAvailableProcedures] = useState(procedures);
  const form = useFormContext();

  useEffect(() => {
    if (unitsData?.list_units?.length && !selectedUnit) {
      setSelectedUnit(unitsData.list_units[0].id);
    }
  }, [unitsData, selectedUnit]);

  useEffect(() => {
    if (!selectedUnit) {
      return;
    }
    const updatedProcedures = procedures.filter((proc) => proc.unit_id === selectedUnit);
    setAvailableProcedures(updatedProcedures);

    // Clear procedure_ids for all existing procedures when unit changes
    const currentProcedures = form.getValues('procedures') || [];
    const updatedProceduresWithClearedIds = currentProcedures.map((procedure: any) => {
      const { procedure_ids, ...rest } = procedure;
      return rest; // Return procedure object without procedure_ids property
    });

    // Update form values
    form.setValue('procedures', updatedProceduresWithClearedIds);
  }, [selectedUnit, procedures, form]);

  const procedureListKey = availableProcedures.map((p) => p.id).join('-');

  return (
    <div>
      <Label>Unit</Label>
      <Select
        aria-label="Labs"
        selectedKey={selectedUnit}
        onSelectionChange={(s: any) => {
          setSelectedUnit(s);
        }}
      >
        <RACButton className="react-aria-Button h-9 w-full rounded-sm">
          <SelectValue className="react-aria-SelectValue text-xs text-neutral-900 capitalize" />
          <ChevronDown />
        </RACButton>
        <Popover>
          <ListBox items={unitsData?.list_units}>
            {(unit) => (
              <ListBoxItem
                className="react-aria-ListBoxItem text-sm"
                id={unit.id}
                textValue={String(unit.id)}
              >
                {unit.description}
              </ListBoxItem>
            )}
          </ListBox>
        </Popover>
      </Select>

      <FieldArray
        name="procedures"
        initialItemsCount={1}
      >
        {({append}) => (
          <>
            <FieldArrayItems>
              {({index}) => (
                <div className="border-brand-100 my-4 grid grid-cols-1 gap-4 rounded border p-4">
                  <ProcedureTagList
                    key={`${procedureListKey}-${index}`}
                    availableProcedures={availableProcedures}
                    index={index}
                  />

                  <DateFormField
                    name={`procedures.${index}.provisional_date`}
                    granularity="day"
                    required
                  >
                    <Label className="text-neutral-700">Provisional Date</Label>
                    <Group>
                      <DateInput>{(segment) => <DateSegment segment={segment} />}</DateInput>
                      <RACButton className="react-aria-Button">
                        <CalenderIcon
                          className="h-4 w-4"
                          color="currentColor"
                        />
                      </RACButton>
                    </Group>
                    <Popover className="react-aria-Popover w-max">
                      <Dialog className="p-4">
                        <Calendar />
                      </Dialog>
                    </Popover>
                    <FieldError />
                  </DateFormField>

                  <TextFormField
                    name={`procedures.${index}.note`}
                    className="react-aria-TextField"
                  >
                    <Label className="text-neutral-700">Test/Clinical Notes</Label>
                    <TextArea size="md" />
                  </TextFormField>
                  <div>
                    <Button
                      className="p-0"
                      variant="plain"
                      color="danger"
                      size="small"
                      slot="remove"
                    >
                      <X data-slot="icon" />
                      Remove
                    </Button>
                  </div>
                </div>
              )}
            </FieldArrayItems>
            <div className="my-4">
              <Button
                variant="plain"
                color="brand"
                size="small"
                onPress={() => append({})}
              >
                <Plus data-slot="icon" />
                Add Procedure
              </Button>
            </div>
          </>
        )}
      </FieldArray>
    </div>
  );
}

export default AddProcedureForStudyItem;