table:
  name: dispatch_record
  schema: public
select_permissions:
  - role: user
    permission:
      columns:
        - id
        - is_resolved
        - ack_code
        - ack_message
        - edi
        - fax_number
        - filename
        - message_id
        - provider_number
        - record_id
        - source
        - stage
        - status
        - ur
        - user_id
        - attempts
        - interface_log_id
        - interface_queue_id
        - site_id
        - base64_data
        - failure_reason
        - notes
        - created_at
        - updated_at
        - gofax_outbound_id
      filter:
        site_id:
          _eq: X-Hasura-Site-Id
      allow_aggregations: true
    comment: ""
