table:
  name: study_request
  schema: public
object_relationships:
  - name: cc_doctor
    using:
      foreign_key_constraint_on: report_cc_id
  - name: pas_pt
    using:
      foreign_key_constraint_on: patient_id
  - name: requesting_doctor
    using:
      foreign_key_constraint_on: requesting_doctor_id
  - name: site
    using:
      foreign_key_constraint_on: site_id
array_relationships:
  - name: study_request_items
    using:
      foreign_key_constraint_on:
        column: study_request_id
        table:
          name: study_request_item
          schema: public
insert_permissions:
  - role: user
    permission:
      check:
        site_id:
          _eq: X-Hasura-Site-Id
      columns:
        - appointment_note
        - date_created
        - date_recieved
        - id
        - patient_id
        - pdf_file_name
        - pdf_file_url
        - report_cc_id
        - requesting_doctor_id
        - site_id
        - type
        - urgency
    comment: ""
select_permissions:
  - role: user
    permission:
      columns:
        - appointment_note
        - date_created
        - date_recieved
        - id
        - patient_id
        - pdf_file_name
        - pdf_file_url
        - report_cc_id
        - requesting_doctor_id
        - site_id
        - type
        - urgency
      filter:
        site_id:
          _eq: X-Hasura-Site-Id
    comment: ""
update_permissions:
  - role: user
    permission:
      columns:
        - appointment_note
        - date_created
        - date_recieved
        - id
        - patient_id
        - pdf_file_name
        - pdf_file_url
        - report_cc_id
        - requesting_doctor_id
        - site_id
        - type
        - urgency
      filter:
        site_id:
          _eq: X-Hasura-Site-Id
      check: null
    comment: ""
