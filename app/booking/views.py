import boto3
import uuid
from app import db

from flask import jsonify, current_app, Blueprint, request

from werkzeug.utils import secure_filename
from flask import current_app
from app import csrf
from app.booking.models import StudyRequest
from app.qc.admin import s3_client
from flask import request, jsonify
import boto3
from botocore.exceptions import ClientError
import uuid
import os
from datetime import datetime, timedelta

from config import Config
from botocore.client import Config as BotoConfig
from app.auth.token import get_current_site_id
from flask_jwt_extended import (
    jwt_required
)
from werkzeug.utils import secure_filename
import requests


booking_bp = Blueprint("booking_bp", __name__)

@booking_bp.route("/api/booking/presigned_upload_url", methods=["POST"])
@jwt_required()
def presigned_upload_url():
    """
    Upload PDF file directly to S3 and return the key and filename
    """
    if 'file' not in request.files:
        return jsonify({'error': 'File is required.'}), 400
    
    file = request.files['file']
    if not file or file.filename == '':
        return jsonify({'error': 'No file selected.'}), 400
    
    filename = file.filename
    file_extension = filename.split('.')[-1].lower() if '.' in filename else ''
    if file_extension != 'pdf':
        return jsonify({'error': 'Only PDF files are allowed.'}), 400
    
    unique_filename = f"{uuid.uuid4()}.pdf"
    s3_key = f"bookings/referrals/{unique_filename}"
    
    presigned_post = s3_client.generate_presigned_post(
        Bucket=Config.AWS_S3_BUCKET,
        Key=s3_key,
        Fields={'Content-Type': 'application/pdf'},
        Conditions=[
            {'Content-Type': 'application/pdf'},
            ['content-length-range', 1, 52428800],
        ],
        ExpiresIn=3600 
    )
    
    files = {'file': (unique_filename, file.read(), 'application/pdf')}
    data = presigned_post['fields']
    
    response = requests.post(presigned_post['url'], data=data, files=files)
    
    if response.status_code != 204:
        return jsonify({'error': 'Failed to upload file to S3.'}), 500
            
    return jsonify({
        'key': s3_key,
        'filename': filename
    }), 200

@booking_bp.route("/api/booking/<int:study_request_id>/pdf_view_url", methods=["GET"])
@jwt_required()
def pdf_view_url(study_request_id):
    """
    Get a presigned URL to view/download the PDF file for a study request (SQLAlchemy ORM version)
    """
    from sqlalchemy import text

    study_request = StudyRequest.query.filter(StudyRequest.id == study_request_id).first()

    if not study_request:
        return jsonify({'error': 'Study request not found.'}), 404

    if not study_request.pdf_file_url:
        return jsonify({'error': 'No PDF file found for this study request.'}), 404
    if get_current_site_id() != study_request.site_id:
        return jsonify({'error': 'Unauthorized access to this study request.'}), 403

    presigned_url = s3_client.generate_presigned_url(
        'get_object',
        Params={
            'Bucket': Config.AWS_S3_BUCKET,
            'Key': study_request.pdf_file_url
        },
        ExpiresIn=3600
    )

    return jsonify({
        'pdf_url': presigned_url,
        'expires_in': 3600,
    }), 200